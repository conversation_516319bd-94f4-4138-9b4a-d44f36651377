import pyodbc
import pandas as pd
from openpyxl import load_workbook

# SQL Server connection settings
server = r'.\evolva'  # Your SQL Server address
database = 'GPYGN(Data)'  # Your database name
username = 'sa'  # SQL Server username
password = 'thinkagain'  # SQL Server password
driver = '{ODBC Driver 17 for SQL Server}'  # Or whichever driver you have installed

# Create a connection string
conn_str = f'DRIVER={driver};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# SQL query to execute
query = """
-- Your stored procedure query goes here
EXEC dbo.speVFillSalesTargetAndAchievementBySaleArea 
@SDate='2025-01-01', @EDate='2025-01-31', @iYear=2025, @iMonth=1
"""

# Path to the template Excel file
template_file = r"D:\Automation Files\GP Sales Target & Achievement\GP Yangon Sales Target & Achievement_Master.xlsx"
output_file = r"D:\Automation Files\GP Sales Target & Achievement\sales_target_achievement_formatted.xlsx"

# Connect to SQL Server
conn = pyodbc.connect(conn_str)

# Execute the SQL query and load the results into a pandas DataFrame
df = pd.read_sql(query, conn)

# Load the existing template Excel file
wb = load_workbook(template_file)

# Select the sheet where you want to place the data
sheet = wb.active  # You can specify the sheet name with wb['SheetName']

# Insert the data from the DataFrame into the specified location
# Assuming your template has headers on the first row, starting at column A
for r, row in enumerate(df.values, start=2):  # Start from row 2 (row 1 contains headers)
    for c, value in enumerate(row, start=1):  # Start from column 1 (column A)
        sheet.cell(row=r, column=c, value=value)

# Save the formatted data to a new Excel file
wb.save(output_file)

print("Excel file created and formatted successfully!")

# Close the database connection
conn.close()
